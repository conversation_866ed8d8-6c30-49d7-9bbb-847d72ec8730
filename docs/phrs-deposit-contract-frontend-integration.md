# PHRS充值合约前端对接文档

本文档详细说明了如何在前端应用中集成PHRS充值智能合约，实现用户将PHRS原生币充值到游戏系统的功能。

## 合约基本信息

### 网络信息
- **网络名称**: Pharos Testnet
- **Chain ID**: 688688
- **区块浏览器**: 待确认

### 合约地址
- **代理合约地址**: `******************************************`
- **实现合约地址**: `******************************************`
- **合约类型**: 透明代理可升级合约

> ⚠️ **重要**: 前端应用应始终与代理合约地址交互，不要直接调用实现合约。

### 充值限制
- **最小充值金额**: 0.00001 PHRS
- **最大充值金额**: 1,000,000,000,000,000 PHRS

## 技术架构说明

### PHRS原生币特性
- PHRS是Pharos网络的原生币（类似以太坊的ETH）
- 用户直接发送原生币到合约，无需代币授权
- 不需要处理ERC20代币的approve流程

### 透明代理模式
- 合约支持升级，但用户始终与同一个代理地址交互
- 升级不会影响前端集成代码
- 所有状态数据保存在代理合约中

## 合约ABI

### 核心函数ABI

```json
[
  {
    "inputs": [],
    "name": "deposit",
    "outputs": [],
    "stateMutability": "payable",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "getContractInfo",
    "outputs": [
      {"internalType": "uint256", "name": "minAmount", "type": "uint256"},
      {"internalType": "uint256", "name": "maxAmount", "type": "uint256"},
      {"internalType": "uint256", "name": "contractBalance", "type": "uint256"}
    ],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [{"internalType": "address", "name": "user", "type": "address"}],
    "name": "getUserBalance",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "paused",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "anonymous": false,
    "inputs": [
      {"indexed": true, "internalType": "address", "name": "user", "type": "address"},
      {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"},
      {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}
    ],
    "name": "Deposit",
    "type": "event"
  }
]
```

## 前端集成步骤

### 1. 环境准备

#### 安装依赖
```bash
npm install ethers
# 或
npm install web3
```

#### 网络配置
```javascript
// 使用ethers.js
const provider = new ethers.JsonRpcProvider('YOUR_PHAROS_RPC_URL');

// 或使用MetaMask
if (window.ethereum) {
  const provider = new ethers.BrowserProvider(window.ethereum);
}
```

### 2. 合约连接

```javascript
// 合约配置
const CONTRACT_ADDRESS = '******************************************';
const CONTRACT_ABI = [
  // 使用上面提供的ABI
];

// 连接合约（只读）
const contract = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, provider);

// 连接合约（可写，需要签名者）
const signer = await provider.getSigner();
const contractWithSigner = new ethers.Contract(CONTRACT_ADDRESS, CONTRACT_ABI, signer);
```

### 3. 获取合约信息

```javascript
async function getContractInfo() {
  try {
    // 获取基本信息
    const [minAmount, maxAmount, contractBalance] = await contract.getContractInfo();
    
    // 检查合约是否暂停
    const isPaused = await contract.paused();
    
    return {
      minDepositAmount: ethers.formatEther(minAmount),
      maxDepositAmount: ethers.formatEther(maxAmount),
      contractBalance: ethers.formatEther(contractBalance),
      isPaused
    };
  } catch (error) {
    console.error('获取合约信息失败:', error);
    throw error;
  }
}
```


### 4. 执行充值

```javascript
async function deposit(amountInPHRS) {
  try {
    // 1. 参数验证
    const contractInfo = await getContractInfo();
    
    if (contractInfo.isPaused) {
      throw new Error('合约已暂停，无法充值');
    }
    
    const amount = parseFloat(amountInPHRS);
    const minAmount = parseFloat(contractInfo.minDepositAmount);
    const maxAmount = parseFloat(contractInfo.maxDepositAmount);
    
    if (amount < minAmount) {
      throw new Error(`充值金额太小，最小金额为 ${minAmount} PHRS`);
    }
    
    if (amount > maxAmount) {
      throw new Error(`充值金额太大，最大金额为 ${maxAmount} PHRS`);
    }
    
    // 2. 检查用户余额
    const signer = await provider.getSigner();
    const userAddress = await signer.getAddress();
    const userBalance = await provider.getBalance(userAddress);
    const depositAmount = ethers.parseEther(amountInPHRS);
    
    if (userBalance < depositAmount) {
      throw new Error('账户PHRS余额不足');
    }
    
    // 3. 估算Gas费用
    const gasEstimate = await contractWithSigner.deposit.estimateGas({
      value: depositAmount
    });
    
    const feeData = await provider.getFeeData();
    const estimatedGasCost = gasEstimate * feeData.gasPrice;
    
    console.log(`预估Gas费用: ${ethers.formatEther(estimatedGasCost)} PHRS`);
    
    // 4. 执行充值交易
    const tx = await contractWithSigner.deposit({
      value: depositAmount,
      gasLimit: gasEstimate * 120n / 100n // 增加20%的Gas缓冲
    });
    
    console.log(`交易已提交: ${tx.hash}`);
    
    // 5. 等待交易确认
    const receipt = await tx.wait();
    
    if (receipt.status === 1) {
      console.log('充值成功!');
      
      // 6. 解析充值事件
      const depositEvent = parseDepositEvent(receipt);
      
      return {
        success: true,
        transactionHash: tx.hash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        event: depositEvent
      };
    } else {
      throw new Error('交易失败');
    }
    
  } catch (error) {
    console.error('充值失败:', error);
    throw error;
  }
}
```

### 6. 解析充值事件

```javascript
function parseDepositEvent(receipt) {
  try {
    // 查找Deposit事件
    const depositLog = receipt.logs.find(log => {
      try {
        const parsed = contract.interface.parseLog(log);
        return parsed.name === 'Deposit';
      } catch {
        return false;
      }
    });
    
    if (depositLog) {
      const parsed = contract.interface.parseLog(depositLog);
      return {
        user: parsed.args.user,
        amount: ethers.formatEther(parsed.args.amount),
        timestamp: new Date(Number(parsed.args.timestamp) * 1000)
      };
    }
    
    return null;
  } catch (error) {
    console.error('解析事件失败:', error);
    return null;
  }
}
```
